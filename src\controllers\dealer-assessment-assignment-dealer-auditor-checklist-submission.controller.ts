import {
  UserRepository
} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {v4 as uuidv4} from 'uuid';
import {
  Action,
  DealerAssessmentAssignment,
  DealerAuditorChecklistSubmission,
} from '../models';
import {ActionRepository, DealerAssessmentAssignmentRepository, DealerAuditorChecklistSubmissionRepository, UserProfileRepository, UserRoleAuthorizationRepository, VendorCodeRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';
const CryptoJS = require("crypto-js");
export class DealerAssessmentAssignmentDealerAuditorChecklistSubmissionController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
    @repository(UserRoleAuthorizationRepository) protected userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(DealerAssessmentAssignmentRepository) protected dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
    @repository(ActionRepository) protected actionRepository: ActionRepository,
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,
    @repository(DealerAuditorChecklistSubmissionRepository) protected dealerAuditorChecklistSubmissionRepository: DealerAuditorChecklistSubmissionRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
  ) { }

  @get('/dealer-assessment-assignments/{id}/dealer-auditor-checklist-submission', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment has one DealerAuditorChecklistSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DealerAuditorChecklistSubmission),
          },
        },
      },
    },
  })
  async get(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<DealerAuditorChecklistSubmission>,
  ): Promise<DealerAuditorChecklistSubmission> {
    return this.dealerAssessmentAssignmentRepository.dealerAuditorChecklistSubmission(id).get(filter);
  }

  @post('/dealer-assessment-assignments/{id}/dealer-auditor-checklist-submission', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerAuditorChecklistSubmission)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof DealerAssessmentAssignment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAuditorChecklistSubmission, {
            title: 'NewDealerAssessmentAssignment',
            exclude: ['id'],
          }),
        },
      },
    }) formData: any // Accepts formData object
  ): Promise<DealerAuditorChecklistSubmission> {

    // Ensure response is properly parsed
    let parsedResponse;
    try {
      parsedResponse = typeof formData.response === 'string' ? JSON.parse(formData.response) : formData.response;
    } catch (error) {
      throw new HttpErrors.BadRequest('Invalid JSON format in response field.');
    }

    // Create submission entry in DealerAuditorChecklistSubmission
    const dealerAuditorChecklistSubmission = {
      nonCompliances: formData?.nonCompliances || null,
      goodPractices: formData?.goodPractices || null,
      improvements: formData?.improvements || null,
      type: formData.type,
      dealerAssessmentAssignmentId: id,
      response: JSON.stringify(parsedResponse),
      formId: formData.formId,
      dealerId: formData.dealerId,
      created_on: formData.created_on || new Date().toISOString(),
      created_by: formData.created_by,
      score: formData.score,
      status: "Under Review",
      vendorId: formData.vendorId
    };

    const DAAData = await this.dealerAssessmentAssignmentRepository.findById(id);

    const auditMaskId = 'MSI-' + (DAAData?.vendorCode || 'NA') + '-' + DateTime.fromISO(DAAData.created_on || '', {zone: 'Asia/Calcutta'}).toFormat('ddMyyyy')



    const createdSubmission = await this.dealerAssessmentAssignmentRepository
      .dealerAuditorChecklistSubmission(id)
      .create(dealerAuditorChecklistSubmission);



    return createdSubmission;
  }

  @post('/send-dealer-report-ack-mail/{id}', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerAuditorChecklistSubmission)}},
      },
    },
  })
  async sendDealerReportAckMail(
    @param.path.number('id') id: typeof DealerAssessmentAssignment.prototype.id,
    @requestBody({
      required: true,
      content: {

        'application/json': {
          schema: {
            type: 'object',
            properties: {
              requestId: {type: 'number'},
            },
            required: ['requestId'],
          }
        },
      },
    }) formData: {requestId: number} // Accepts formData object
  ): Promise<any> {
    const {requestId} = formData
    const dealerAuditorChecklist = await this.dealerAuditorChecklistSubmissionRepository.findById(id)
    if (dealerAuditorChecklist && dealerAuditorChecklist.type === 2) {
      try {
        const vendor = await this.vendorCodeRepository.findById(dealerAuditorChecklist.vendorId)


        if (vendor) {
          const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendor.userProfileId}})
          if (vendorSpoc.length && vendorSpoc[0].email) {
            const headMailids = this.userProfileController.extractDealerHeadValidEmails(vendor, ['aps', 'ao', 'sales', 'service'], ['areaManagerMailId', 'areaCommercialManagerMailId'])
            const roles = await this.userRoleAuthorizationRepository.execute(

              `SELECT * FROM UserRoleAuthorization
      WHERE roles IS NOT NULL
        AND JSON_LENGTH(roles) > 0
        AND JSON_CONTAINS(roles, ?, '$')`,
              [JSON.stringify([13])]
            )

            const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
            const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
            const sectionAdminMailIds = headUserData.map((x: any) => x.email).filter((x: any) => x)
            const dealerSpoc = [vendorSpoc?.[0]?.email || '', ...headMailids]
            const composeSubject = 'Acknowledgement – Receipt of MSI Assessment Report for' + vendor.dealerName + `( ${vendor.code} )`
            const score = JSON.parse(dealerAuditorChecklist?.score || '{}')?.overallScore || null
            const message = dealerAuditorChecklist?.dealerAssessmentAssignmentId || ''
            const hash = CryptoJS.AES.encrypt(message.toString(), 'e!sq6kee4deaassid').toString()
            const composeBody = `Dear TVS Team,

Thank you for sharing the MSI Assessment Report for ${vendor?.dealerName || 'Dealer'} (${vendor.code || ''}).

We acknowledge the receipt of the report and have reviewed the observations shared.We will proceed to review the action plan provided and provide feedback accordingly.

As advised, we will upload the action plan and provide status updates through the Navigos platform.

We appreciate the valuable feedback and look forward to further strengthening our sustainability practices in collaboration with the TVS team`

            const subject = 'MSI Assessment Report – ' + vendor.dealerName + `( ${vendor.code} )`
            const body = `
  <p>Dear ${vendor?.dealerName || 'Dealer'}</p>
  <p>Thank you for hosting us for the Onsite MSI Assessment.
  We truly appreciate the opportunity to visit your site, interact with your team.
 </p>

  <strong>Rating Achieved:  ${vendor?.dealerName || 'Dealer'} ( ${vendor.code || ''}) - ${!(score != null) ? 'NA' : score >= 85 ? 'Platinum' : score > 70 ? 'Gold' : score > 55 ? 'Silver' : 'Not Met'}</strong>


<p><img src="https://api.eisqr.com/docs/1745664269282msi_score_board.jpg" width="300"/></p>
  <p>For the detailed assessment report and the calibration form with your responses, either log in to the
  <a href="https://tvsmotor-partner.eisqr.com"> EiSqr – TVS Partner Platform </a> using the credentials previously shared, otherwise <a href="https://tvsmotor-partner.eisqr.com/report/dealer?token=${encodeURIComponent(hash)}"> View Report </a> here </p>

  <p><strong>Next Steps:</strong></p>
  <ol>
    <li>Review the assessment report and acknowledge the receipt of the report by <a href="mailto:${[...sectionAdminMailIds, ...headMailids].join(',')}?subject=${encodeURIComponent(composeSubject)}&body=${encodeURIComponent(composeBody)}"> <b>clicking</b>
</a>.</li>
    <li>Understand your current standing on the MSI framework.</li>
    <li>Identify areas requiring improvement for better alignment with sustainability goals.</li>
    <li> Plan and implement necessary corrective actions and Close the action plan along with uploading evidence (Photos,documents etc) in the portal.</li>
  </ol>

  <p>Your proactive engagement in implementing the action plan will support continuous improvement and help enhance your sustainability rating in future assessments.</p>

  <p>If you have any clarifications or require assistance, please feel free to contact <strong><EMAIL></strong> or <strong><EMAIL></strong></p>

  <p>
We appreciate your cooperation and continued commitment to quality and compliance.
</p>
  <p style='margin: 5px 0px;'>Best regards,</p>
  <p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
 <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
`;


            return await this.sqsService.sendEmail(dealerSpoc, subject, body, [...sectionAdminMailIds]).then(async (info) => {
              const reportMailStatus = dealerAuditorChecklist.reportMailStatus || []
              reportMailStatus.push({userId: requestId, date: new Date().toISOString()})
              await this.dealerAuditorChecklistSubmissionRepository.updateById(dealerAuditorChecklist.id, {reportMailStatus})

              return {status: true, message: 'Report Mail sent successfully'};
            })
              .catch(() => {
                return {status: false, message: 'There was an issue while attempting to send the report acknowledgement email. Please try again'};
              });
          } else {
            return {status: false, message: 'There was an issue while attempting to send the report acknowledgement email because of Missing Dealer Spoc Details'}
          }
        } else {
          return {status: false, message: 'There was an issue while attempting to send the report acknowledgement email because of Missing Dealer Details'}
        }
      } catch {
        return {status: false, message: 'There was an issue while attempting to send the report acknowledgement email'}
      }
    } else if (dealerAuditorChecklist) {
      return {status: false, message: 'Report Not Approved'}
    } else {
      return {status: false, message: 'Report Not Found'}
    }

  }


  @post('/dealer-assessment-assignments/{id}/dealer-auditor-checklist-review-submission', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerAuditorChecklistSubmission)}},
      },
    },
  })
  async updateIt(
    @param.path.number('id') id: typeof DealerAssessmentAssignment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAuditorChecklistSubmission, {
            title: 'NewDealerAssessmentAssignment',
            exclude: ['id'],
          }),
        },
      },
    }) formData: any // Accepts formData object
  ): Promise<any> {
    let parsedResponse;
    try {
      parsedResponse = typeof formData.response === 'string' ? JSON.parse(formData.response) : formData.response;
    } catch (error) {
      throw new HttpErrors.BadRequest('Invalid JSON format in response field.');
    }


    const DAAData = await this.dealerAssessmentAssignmentRepository.findById(id);

    const auditMaskId = 'MSI-' + (DAAData?.vendorCode || 'NA') + '-' + DateTime.fromISO(DAAData.created_on || '', {zone: 'Asia/Calcutta'}).toFormat('yyyyMd')

    if (Array.isArray(parsedResponse)) {
      for (const checklist of parsedResponse) {
        if (checklist.questions && Array.isArray(checklist.questions)) {
          for (const question of checklist.questions) {

            // Extract selected options


            // Handle actions if available
            if (question.actions) {

              const {actionToBeTaken, evidence, dueDate, personResponsible} = question.actions;
              const labelMatch = question.label.match(/^([A-Z]\d+)\./);
              const extractedLabel = labelMatch ? labelMatch[1] : 'N';

              const submissionReferenceId = `${auditMaskId}-${extractedLabel}`;
              // Create Action entity
              const actionData = {
                applicationId: `DAA`,
                maskId: submissionReferenceId,
                application: 'DealerAssessmentAssignment',
                actionType: 'Checklist Submission',
                actionToBeTaken: actionToBeTaken || '',
                applicationDetails: {personResponsible: personResponsible, criteria: checklist.criteria, subCriteria: question.subCriteria},
                uploads: evidence || [],
                dueDate: dueDate || '',
                assignedToId: formData.dealerId ? [formData.dealerId] : [],
                status: 'Initiated' as 'Initiated' | 'In Progress' | 'Rejected' | 'Completed' | 'Deleted' | 'Archived',
                description: `${question.label}`,
                createdDate: new Date().toISOString(),
                created: `${new Date()}`,
                updated: `${new Date()}`,
                remarks: question.remarks || '',
                objectId: formData.formId,
                submittedBy: formData.created_by,
                appId: id,
                category: checklist.section || '',
                vendorId: formData.vendorId,
                trackId: uuidv4(),

                // maskId: maskId
              };

              // Save the action in ActionRepository
              await this.actionRepository.create(actionData);
            }
          }
        }
      }
    }
    let existingSubmission;
    try {
      existingSubmission = await this.dealerAssessmentAssignmentRepository
        .dealerAuditorChecklistSubmission(id)
        .get();

    } catch (error) {
      throw new HttpErrors.NotFound('DealerAuditorChecklistSubmission not found for this assessment.');
    }

    const dealerAuditorChecklistSubmission = {
      type: 2,
      response: JSON.stringify(parsedResponse),
      status: "Approved",
      approved_on: new Date().toISOString(),
      approved_by: formData.created_by,
    };
    try {
      const vendor = await this.vendorCodeRepository.findById(existingSubmission.vendorId)


      if (vendor && existingSubmission) {
        const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendor.userProfileId}})
        if (vendorSpoc.length && vendorSpoc[0].email) {
          const headMailids = this.userProfileController.extractDealerHeadValidEmails(vendor, ['aps', 'ao', 'sales', 'service'], ['areaManagerMailId', 'areaCommercialManagerMailId'])

          const dealerSpoc = [vendorSpoc?.[0]?.email || '', ...headMailids]
          const roles = await this.userRoleAuthorizationRepository.execute(

            `SELECT * FROM UserRoleAuthorization
      WHERE roles IS NOT NULL
        AND JSON_LENGTH(roles) > 0
        AND JSON_CONTAINS(roles, ?, '$')`,
            [JSON.stringify([13])]
          )

          const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
          const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
          const sectionAdminMailIds = headUserData.map((x: any) => x.email).filter((x: any) => x)
          const composeSubject = 'Acknowledgement – Receipt of MSI Assessment Report for' + vendor.dealerName + `( ${vendor.code} )`
          const score = JSON.parse(existingSubmission?.score || '{}')?.overallScore || null
          const message = existingSubmission?.dealerAssessmentAssignmentId || ''
          const hash = CryptoJS.AES.encrypt(message.toString(), 'e!sq6kee4deaassid').toString()
          const composeBody = `Dear TVS Team,

Thank you for sharing the MSI Assessment Report for ${vendor?.dealerName || 'Dealer'} (${vendor.code || ''}).

We acknowledge the receipt of the report and have reviewed the observations shared.We will proceed to review the action plan provided and provide feedback accordingly.

As advised, we will upload the action plan and provide status updates through the Navigos platform.

We appreciate the valuable feedback and look forward to further strengthening our sustainability practices in collaboration with the TVS team`

          const subject = 'MSI Assessment Report – ' + vendor.dealerName + `( ${vendor.code} )`
          const body = `
  <p>Dear ${vendor?.dealerName || 'Dealer'}</p>
  <p>Thank you for hosting us for the Onsite MSI Assessment.
  We truly appreciate the opportunity to visit your site, interact with your team.
 </p>

  <strong>Rating Achieved:  ${vendor?.dealerName || 'Dealer'} ( ${vendor.code || ''}) - ${!(score != null) ? 'NA' : score >= 85 ? 'Platinum' : score > 70 ? 'Gold' : score > 55 ? 'Silver' : 'Not Met'}</strong>


<p><img src="https://api.eisqr.com/docs/1745664269282msi_score_board.jpg" width="300"/></p>

    <p>For the detailed assessment report and the calibration form with your responses, either log in to the
  <a href="https://tvsmotor-partner.eisqr.com"> EiSqr – TVS Partner Platform </a> using the credentials previously shared, otherwise <a href="https://tvsmotor-partner.eisqr.com/report/dealer?token=${encodeURIComponent(hash)}"> View Report </a> here </p>

  <p><strong>Next Steps:</strong></p>
  <ol>
    <li>Review the assessment report and acknowledge the receipt of the report by <a href="mailto:${[...sectionAdminMailIds, ...headMailids].join(',')}?subject=${encodeURIComponent(composeSubject)}&body=${encodeURIComponent(composeBody)}"> <b>clicking</b>
</a>.</li>
    <li>Understand your current standing on the MSI framework.</li>
    <li>Identify areas requiring improvement for better alignment with sustainability goals.</li>
    <li> Plan and implement necessary corrective actions and Close the action plan along with uploading evidence (Photos,documents etc) in the portal.</li>
  </ol>

  <p>Your proactive engagement in implementing the action plan will support continuous improvement and help enhance your sustainability rating in future assessments.</p>

  <p>If you have any clarifications or require assistance, please feel free to contact <strong><EMAIL></strong> or <strong><EMAIL></strong></p>

  <p>
We appreciate your cooperation and continued commitment to quality and compliance.
</p>
  <p style='margin: 5px 0px;'>Best regards,</p>
  <p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
 <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
`;

          this.sqsService.sendEmail(dealerSpoc, subject, body, [...sectionAdminMailIds])
            .then(info => console.log('Email sent:', info))
            .catch(err => console.error('Error sending email:', err));
        }
      }
    } catch {
      console.log('catch')
    }
    return this.dealerAssessmentAssignmentRepository
      .dealerAuditorChecklistSubmission(id)
      .patch(dealerAuditorChecklistSubmission, {where: {id: existingSubmission.id}});

  }


  @post('/upload-checklist-actions-json', {
    responses: {
      '200': {
        description: 'Checklist actions imported',
        content: {'application/json': {schema: {type: 'object'}}},
      },
    },
  })
  async uploadChecklistActionsJson(
    @requestBody({description: 'Final action format from frontend', required: true})
    data: any[],
  ): Promise<object> {
    const results = [];

    for (const row of data) {
      try {
        // Simple validation
        if (!row.actionToBeTaken || !row.maskId) {
          results.push({row: row.maskId || 'N/A', status: 'Skipped', reason: 'Missing required fields'});
          continue;
        }

        // Optional: ensure ISO strings if needed
        const actionData = {
          ...row,
          dueDate: row.dueDate ? new Date(row.dueDate).toISOString() : '',
          createdDate: row.createdDate ? new Date(row.createdDate).toISOString() : new Date().toISOString(),
          updated: new Date().toString(),
          created: new Date().toString(),
        };

        await this.actionRepository.create(actionData);

        results.push({row: row.maskId, status: 'Imported'});
      } catch (error) {
        results.push({
          row: row.maskId || 'N/A',
          status: 'Error',
          error: error.message,
        });
      }
    }

    return {message: 'Processed rows', results};
  }


  @patch('/dealer-assessment-assignments/{id}/dealer-auditor-checklist-submission', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment.DealerAuditorChecklistSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAuditorChecklistSubmission, {partial: true}),
        },
      },
    })
    dealerAuditorChecklistSubmission: Partial<DealerAuditorChecklistSubmission>,
    @param.query.object('where', getWhereSchemaFor(DealerAuditorChecklistSubmission)) where?: Where<DealerAuditorChecklistSubmission>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.dealerAuditorChecklistSubmission(id).patch(dealerAuditorChecklistSubmission, where);
  }


  @patch('/dealer-actions-submit/{actionId}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateById(
    @param.path.number('actionId') actionId: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Action,
  ): Promise<void> {

    // Step 1: Fetch action details
    const currentActionData = await this.actionRepository.findById(actionId);

    // Step 2: Fetch vendor detailsc

    const vendorCodeData = await this.vendorCodeRepository.findById(Number(currentActionData.vendorId));

    if (!vendorCodeData) {
      throw new Error('Vendor not found');
    }
    // Step 3: Fetch vendorCode details


    // Step 4: Determine which field to use based on category
    let areaManagerMailId = '';
    if (currentActionData.category === '1' && vendorCodeData.sales) {
      areaManagerMailId = vendorCodeData.sales.areaManagerMailId!;
    } else if (currentActionData.category === '2' && vendorCodeData.service) {
      areaManagerMailId = vendorCodeData.service.areaManagerMailId!;
    } else {
      throw new Error('Invalid category or missing data in vendorCode');
    }

    const roles = await this.userRoleAuthorizationRepository.execute(

      `SELECT * FROM UserRoleAuthorization
      WHERE roles IS NOT NULL
        AND JSON_LENGTH(roles) > 0
        AND JSON_CONTAINS(roles, ?, '$')`,
      [JSON.stringify([13])]
    )

    const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
    const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
    const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)
    // Step 5: Fetch user ID using email
    // const user = await this.userRepository.findOne({
    //   where: {email: areaManagerMailId},
    // });

    // if (!user) {
    //   throw new Error(`User not found for email: ${areaManagerMailId}`);
    // }

    // Step 6: Fetch UserProfile ID using user ID
    // const userProfile = await this.userProfileRepository.findOne({
    //   where: {userId: user.id},
    // });

    // if (!userProfile) {
    //   throw new Error(`User profile not found for user ID: ${user.id}`);
    // }

    // const userProfileId = userProfile.id; // Returning the UserProfile ID
    const actionData = {
      applicationId: `DAA`,
      maskId: currentActionData.maskId,
      application: 'DealerAssessmentAssignment',
      actionType: 'Checklist Submission Review',
      actionTaken: action.actionTaken,
      evidence: action.evidence,
      actionToBeTaken: currentActionData.actionToBeTaken || '',
      applicationDetails: currentActionData.applicationDetails,
      uploads: currentActionData.uploads || [],
      dueDate: currentActionData.dueDate || '',
      assignedToId: headUserIds,
      status: 'Initiated' as 'Initiated' | 'In Progress' | 'Rejected' | 'Completed' | 'Deleted' | 'Archived',
      description: currentActionData.description,
      createdDate: new Date().toISOString(),
      created: `${new Date()}`,
      updated: `${new Date()}`,
      remarks: currentActionData.remarks || '',
      objectId: currentActionData.objectId,
      submittedBy: currentActionData.assignedToId ? currentActionData.assignedToId[0] : undefined,
      appId: currentActionData.appId,
      category: currentActionData.category || '',
      vendorId: currentActionData.vendorId,
      trackId: currentActionData.trackId,
      // maskId: maskId
    };

    // Save the action in ActionRepository
    await this.actionRepository.create(actionData);

    await this.actionRepository.updateById(actionId, {status: 'Completed', actionTaken: action.actionTaken, evidence: action.evidence});
  }


  @patch('/dealer-actions-return/{actionId}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateReturnById(
    @param.path.number('actionId') actionId: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Action,
  ): Promise<void> {

    // Step 1: Fetch action details
    const currentActionData = await this.actionRepository.findById(actionId);

    // Step 2: Fetch vendor details

    const actionData = {
      applicationId: `DAA`,
      maskId: currentActionData.maskId,
      application: 'DealerAssessmentAssignment',
      actionType: 'Checklist Submission Returned',
      actionToBeTaken: currentActionData.actionToBeTaken || '',
      applicationDetails: currentActionData.applicationDetails,
      uploads: currentActionData.evidence || [],
      dueDate: currentActionData.dueDate || '',
      assignedToId: currentActionData.vendorId ? [currentActionData.vendorId] : [],
      status: 'Initiated' as 'Initiated' | 'In Progress' | 'Rejected' | 'Completed' | 'Deleted' | 'Archived',
      description: currentActionData.description,
      createdDate: new Date().toISOString(),
      created: `${new Date()}`,
      updated: `${new Date()}`,
      remarks: currentActionData.remarks || '',
      objectId: currentActionData.objectId,
      submittedBy: currentActionData.assignedToId ? currentActionData.assignedToId[0] : undefined,
      appId: currentActionData.appId,
      category: currentActionData.category || '',
      vendorId: currentActionData.vendorId,
      comments: action.comments,
      trackId: currentActionData.trackId,
      // maskId: maskId
    };
    const vendorData = await this.vendorCodeRepository.findById(Number(currentActionData.vendorId));

    if (vendorData) {
      const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}})

      if (vendorSpoc.length && vendorSpoc[0].email) {
        const headMailids = this.userProfileController.extractDealerHeadValidEmails(vendorData, ['aps', 'ao', 'sales', 'service'], ['areaManagerMailId', 'areaCommercialManagerMailId'])

        const dealerSpoc = vendorSpoc[0]?.email || ''
        console.log('assa')
        const subject = 'Re-submission Required – MSI Calibration Action Plan'
        const body = `
<p>Dear ${vendorData.dealerName}</p>
       <p style='margin: 5px 0px;'>This is in reference to the <strong>MSI Calibration</strong> conducted at your dealership (${vendorData.dealerName}, Code: ${vendorData.code}). The action plans submitted in response to the Calibration findings have been reviewed, but have been reverted due to the following reasons:<strong"> ${action.comments}</strong> <p>
<p style='margin: 5px 0px;'>We kindly request you to <strong>revisit the highlighted action points</strong>, incorporate the necessary corrections or improvements, and resubmit the updated tracker along with appropriate supporting documentation.</p>
<p style='margin: 5px 0px;'>All relevant details are available on the <a href="https://tvsmotor-partner.eisqr.com" >EiSqr – TVS Partner Platform </a>. </p>
<p style='margin: 5px 0px;'>Should you need any assistance or clarification, please feel free to reach out to us at <strong><EMAIL></strong> and mark a copy to <strong><EMAIL></strong></p>
<p style='margin: 5px 0px;'>Best regards,</p>
<p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
<p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
       `

        await this.sqsService.sendEmail([dealerSpoc, ...headMailids], subject, body, ["<EMAIL>", "<EMAIL>"])
      }
    }



    // Save the action in ActionRepository
    await this.actionRepository.create(actionData);

    await this.actionRepository.updateById(actionId, {status: 'Completed', comments: action.comments});
  }

  @patch('/dealer-actions-approved/{actionId}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateApprovedById(
    @param.path.number('actionId') actionId: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, {partial: true}),
        },
      },
    })
    action: Action,
  ): Promise<any> {
    // Step 1: Fetch current action
    const currentActionData = await this.actionRepository.findById(actionId);
    if (!currentActionData) {
      throw new HttpErrors.NotFound('Action not found');
    }


    // Step 2: Fetch checklist submission by dealerAssessmentAssignmentId (appId)
    const submission = await this.dealerAuditorChecklistSubmissionRepository.findOne({
      where: {dealerAssessmentAssignmentId: currentActionData.appId},
    });

    if (!submission) {
      throw new HttpErrors.NotFound('Checklist submission not found');
    }

    // // Define interfaces for response structure
    interface QuestionOption {
      label: string;
      checked: number;
      score: number;
      [key: string]: unknown;
    }

    interface Question {
      label: string;
      type?: string;
      options: QuestionOption[];
      score?: number | string;
      [key: string]: unknown;
    }

    interface ResponseGroup {
      questions: Question[];
      score?: number;
      section?: number;
      criteria?: string;
      subCriteria?: string;
      [key: string]: unknown;
    }


    // // Step 3: Parse submission response - use updatedResponse if available, otherwise use response
    let parsedResponse: ResponseGroup[] = [];
    let testResponse: any = []
    try {
      // Check if updatedResponse exists and is not null, otherwise fall back to response
      const isInvalid = (val: any) => val === null || val === undefined || val === '' || val === '[]';

      const responseData = !isInvalid(submission.updatedResponse)
        ? submission.updatedResponse
        : (!isInvalid(submission.response) ? submission.response : '[]');
      testResponse = responseData
      // Additional validation for string data
      if (typeof responseData === 'string') {
        console.log('1')
        // Check if string is empty or just whitespace
        if (!responseData.trim()) {
          console.log('2')

          parsedResponse = [];
        } else {
          // Check if string looks like valid JSON
          const trimmedData = responseData.trim();

          if (!trimmedData.startsWith('[') && !trimmedData.startsWith('{')) {
            console.log('3')

            console.warn('Response data does not appear to be valid JSON:', trimmedData.substring(0, 100));
            parsedResponse = [];
          } else {
            console.log('4')

            // Try to fix common JSON issues
            let cleanedData = trimmedData;

            // Remove any trailing commas before closing brackets/braces
            cleanedData = cleanedData.replace(/,(\s*[}\]])/g, '$1');

            // Ensure the JSON is properly closed
            if (cleanedData.startsWith('[') && !cleanedData.endsWith(']')) {
              cleanedData += ']';
            } else if (cleanedData.startsWith('{') && !cleanedData.endsWith('}')) {
              cleanedData += '}';
            }

            parsedResponse = JSON.parse(cleanedData);
          }
        }
      } else if (Array.isArray(responseData)) {
        parsedResponse = responseData;
      } else if (responseData && typeof responseData === 'object') {
        // If it's an object but not an array, try to extract array from it
        parsedResponse = Array.isArray((responseData as any).data) ? (responseData as any).data : [responseData];
      } else {
        parsedResponse = [];
      }

      // Handle nested array structure (common in some submissions)
      if (Array.isArray(parsedResponse) && parsedResponse.length === 1 && Array.isArray(parsedResponse[0])) {
        parsedResponse = parsedResponse[0];
      }

      // Validate that parsedResponse is an array
      if (!Array.isArray(parsedResponse)) {
        console.warn('Parsed response is not an array, converting to array');
        parsedResponse = parsedResponse ? [parsedResponse] : [];
      }

    } catch (err) {
      console.error('Error parsing response data:', err);
      console.error('Response data type:', typeof (submission.updatedResponse ?? submission.response));
      console.error('Response data content (first 500 chars):',
        String(submission.updatedResponse ?? submission.response).substring(0, 500));
      console.error('Response data length:',
        String(submission.updatedResponse ?? submission.response).length);

      // Instead of throwing an error, use empty array and log warning
      console.warn('Using empty array due to parsing error');
      parsedResponse = [];
    }



    // // Step 4: Find and update matching question

    let updatedQuestionFound = false;

    parsedResponse = parsedResponse.map((group: ResponseGroup) => {
      if (group.type !== 'checklist-group' || !Array.isArray(group.questions)) return group;

      let delta = 0;

      const updatedQuestions = group.questions.map((question: Question) => {

        if (question.label === currentActionData.description && group.section === parseInt(currentActionData.category ?? '0')) {
          updatedQuestionFound = true;
          console.log(question)


          const isNegative = question.type === 'Negative';
          const desiredLabel = isNegative ? 'No' : 'Yes';

          const updatedOptions = (question.options ?? []).map((opt: QuestionOption) => ({
            ...opt,
            checked: opt.label === desiredLabel ? 1 : 0,
          }));

          const selectedOption = updatedOptions.find(opt => opt.checked === 1);
          const oldSelectedOption = (question.options ?? []).find(opt => opt.checked === 1);
          const newScore = selectedOption?.score ?? 0;
          const oldScore = oldSelectedOption?.score ?? 0;
          // Calculate score delta
          delta = newScore - oldScore;
          console.log(newScore, oldScore)
          return {
            ...question,
            options: updatedOptions,
            score: newScore,
          };
        }

        return question;
      });

      // Instead of recomputing score from scratch, just apply delta
      const groupScore = Number(group.score ?? 0) + delta;

      return {
        ...group,
        questions: updatedQuestions,
        score: groupScore,
      };
    });

    if (!updatedQuestionFound) {
      console.warn(`Warning: No question found matching action description: ${currentActionData.description}`);
    }

    // // Step 6: Recalculate section-level scores
    const salesScore = parsedResponse
      .filter(g => g.section === 1)
      .reduce((sum, g) => sum + (g.score ?? 0), 0);

    const serviceScore = parsedResponse
      .filter(g => g.section === 2)
      .reduce((sum, g) => sum + (g.score ?? 0), 0);

    // Calculate overall score as average of sales and service scores
    const overallScore = (salesScore + serviceScore) / 2;




    // // Step 8: Save updated response and score
    await this.dealerAuditorChecklistSubmissionRepository.updateById(submission.id, {
      updatedResponse: JSON.stringify(parsedResponse),
      updatedScore: JSON.stringify({"overallScore": overallScore, "salesScore": salesScore, "serviceScore": serviceScore}),
    });

    // // Step 9: Mark action as completed with optional comment
    await this.actionRepository.updateById(actionId, {
      status: 'Completed',
      comments: action.comments,
    });
    return {"overallScore": overallScore, "salesScore": salesScore, "serviceScore": serviceScore, parsedResponse: submission}
  }



  @del('/dealer-assessment-assignments/{id}/dealer-auditor-checklist-submission', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment.DealerAuditorChecklistSubmission DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(DealerAuditorChecklistSubmission)) where?: Where<DealerAuditorChecklistSubmission>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.dealerAuditorChecklistSubmission(id).delete(where);
  }
}


