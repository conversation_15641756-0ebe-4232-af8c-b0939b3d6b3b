import {inject} from '@loopback/core';
import {DateTime} from 'luxon';
import {ParquetReader} from 'parquetjs-lite';
import {EmployeeData} from '../models';
import {EmployeeDataRepository} from '../repositories';

export class EmployeeDataService {
  constructor(
    @inject('repositories.EmployeeDataRepository') private employeeDataRepository: EmployeeDataRepository,
  ) { }

  // Function to map grade to EmployeeGrade based on your table
  private mapGradeToEmployeeGrade(grade: string, employeeCategory?: string, employeeRoleType?: string): string {
    // Only map grades for Permanent Employees, all others get "Other"
    if (employeeCategory !== 'Permanent' || employeeRoleType !== 'Employee') {
      return 'Other';
    }

    if (!grade) return 'Non-Management';

    // Clean the input grade - remove extra spaces and normalize dashes
    const inputGrade = grade.toString().toUpperCase().trim().replace(/\s*-\s*/g, '-');

    // Direct mapping based on your table
    const gradeToCategory: Record<string, string> = {
      // Senior Management grades from your table
      'D2': 'Senior Management',
      'D4': 'Senior Management',
      'D1': 'Senior Management',
      'D3': 'Senior Management',
      'D5': 'Senior Management',
      'DA/SW9': 'Senior Management',
      'DA/SW10': 'Senior Management',
      'SP2': 'Senior Management',
      'SP3': 'Senior Management',

      // Middle Management grades from your table
      'B3': 'Middle Management',
      'C1': 'Middle Management',
      'C2': 'Middle Management',
      'C3': 'Middle Management',
      'DA/SW4': 'Middle Management',
      'DA/SW5': 'Middle Management',
      'DA/SW6': 'Middle Management',
      'DA/SW7': 'Middle Management',
      'DA/SW8': 'Middle Management',
      'SS4': 'Middle Management',
      'SS5': 'Middle Management',
      'SP1': 'Middle Management',

      // Non-Management grades from your table
      'A3': 'Non-Management',
      'B1': 'Non-Management',
      'B2': 'Non-Management',
      'A1': 'Non-Management',
      'A2': 'Non-Management',
      'DA/SW1': 'Non-Management',
      'DA/SW2': 'Non-Management',
      'DA/SW3': 'Non-Management',
      'SS1': 'Non-Management',
      'SS2': 'Non-Management',
      'SS3': 'Non-Management',

      // Additional grades found in your data that need categorization
      // Based on the permanentOtherGrades list, these appear to be mostly Non-Management
      'TT': 'Non-Management',
      'TH': 'Non-Management',
      'N4': 'Non-Management',
      'CX': 'Non-Management',
      'TP': 'Non-Management',
      'TM': 'Non-Management',
      'N3': 'Non-Management',
      'ZZ': 'Non-Management',
      'CT': 'Non-Management',
      'AC - C': 'Non-Management',
      'AM - R': 'Non-Management',
      'AM - C': 'Non-Management',
      'AN - C': 'Non-Management',
      'AN - R': 'Non-Management',
      'S2': 'Non-Management',
      'S3': 'Non-Management',
      'S1': 'Non-Management',
      'S4': 'Non-Management',
      'AM - N': 'Non-Management',
      'AC - R': 'Non-Management',
      'AC - N': 'Non-Management',
      'AG - C': 'Non-Management',
      'DA2': 'Non-Management',
      'DA1': 'Non-Management',
      'DA4': 'Non-Management',
      'DA3': 'Non-Management',
      'AM - W': 'Non-Management',
      'AM - D': 'Non-Management',
      'AM - U': 'Non-Management',
      'AC - U': 'Non-Management',
      'AC - W': 'Non-Management',
      'AN - N': 'Non-Management',
      'AN - D': 'Non-Management',
      'AC - D': 'Non-Management',
      'AN - U': 'Non-Management',
      'TK': 'Non-Management',
    };

    // Check if grade exists in mapping
    const category = gradeToCategory[inputGrade];
    if (category) return category;

    // For any other D grade pattern (D followed by numbers) - Senior Management
    if (inputGrade.match(/^D\d+$/)) {
      return 'Senior Management';
    }

    // For Permanent Employees: If grade not found in mapping, default to Non-Management
    return 'Non-Management';
  }

  // Function to determine EmployeeRoleType based on contract_type
  private getEmployeeRoleType(contractType: string): string {
    if (!contractType || contractType.trim() === '') return 'Worker';
    return contractType === '01_Permanent' ? 'Employee' : 'Worker';
  }

  // Function to determine EmployeeCategory based on employee_type
  private getEmployeeCategory(employeeType: string): string {
    if (!employeeType) return 'Other than Permanent';
    return employeeType.trim() === 'White Collar' ? 'Permanent' : 'Other than Permanent';
  }

  // Process raw employee data from parquet buffer for type === 8
  async processEmployeeDataFromBuffer(buffer: Buffer): Promise<{validRecords: EmployeeData[], totalCount: number, invalidCount: number, invalidReasons: string[]}> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const employeeRecords: EmployeeData[] = [];
    const invalidReasons: string[] = [];
    let record;
    let totalCount = 0;
    let invalidCount = 0;

    try {
      while ((record = await cursor.next())) {
        totalCount++;

        // Store converted data based on business logic
        const employeeRoleType = this.getEmployeeRoleType(record.contract_type);
        const employeeCategory = this.getEmployeeCategory(record.employee_type);

        const employeeData: Partial<EmployeeData> = {
          employeeId: record.employee_id,
          EmployeeRoleType: employeeRoleType, // Converted: "Employee" or "Worker"
          EmployeeCategory: employeeCategory, // Converted: "Permanent" or "Other than Permanent"
          EmployeeGender: record.gender,
          EmployeeAge: record.age,
          EmployeeDOJ: record.date_of_joining,
          EmployeeDOE: record.date_of_exit || null,
          locationType: record.location_type,
          officeCity: record.office_city,
          officeLocation: record.office_location,
          raw_grade: record.grade, // Store raw grade
          EmployeeGrade: this.mapGradeToEmployeeGrade(record.grade, employeeCategory, employeeRoleType), // Store converted grade classification
          employeeStatus: record.date_of_exit ? 'Inactive' : 'Active',
          syncDate: DateTime.utc().toString(),
          created_on: DateTime.utc().toString(),
          userProfileId: 289,
          locationId: 103,
        };

        // Enhanced validation with detailed reasons
        const validationErrors: string[] = [];


        if (!employeeData.EmployeeGender) {
          validationErrors.push('Missing gender');
        }
        if (!employeeData.EmployeeAge) {
          validationErrors.push('Missing age');
        }
        if (!employeeData.EmployeeDOJ) {
          validationErrors.push('Missing date_of_joining');
        }
        if (!employeeData.EmployeeCategory) {
          validationErrors.push('Missing EmployeeCategory');
        }
        if (!employeeData.EmployeeRoleType) {
          validationErrors.push('Missing EmployeeRoleType');
        }

        if (validationErrors.length === 0) {
          employeeRecords.push(new EmployeeData(employeeData));
        } else {
          invalidCount++;
          invalidReasons.push(`Record ${totalCount}: ${validationErrors.join(', ')}`);
        }
      }
    } catch (error) {
      console.error('Error processing employee data from parquet:', error);
      throw error;
    } finally {
      await reader.close();
    }

    return {
      validRecords: employeeRecords,
      totalCount,
      invalidCount,
      invalidReasons
    };
  }

  // Store employee data in batches
  async storeEmployeeData(employeeRecords: EmployeeData[], batchSize: number = 1000): Promise<void> {
    const batches = Math.ceil(employeeRecords.length / batchSize);

    for (let i = 0; i < batches; i++) {
      const batch = employeeRecords.slice(i * batchSize, (i + 1) * batchSize);

      try {
        await this.employeeDataRepository.createAll(batch);
      } catch (error) {
        console.error(`Error saving employee data batch ${i + 1}:`, error);
        throw error;
      }
    }
  }

  // Main method to process and store employee data for type === 8
  async processAndStoreEmployeeData(buffer: Buffer, batchSize: number = 1000, fileName?: string): Promise<void> {
    try {
      const fileInfo = fileName ? ` from file: ${fileName}` : '';
      const result = await this.processEmployeeDataFromBuffer(buffer);

      // Only log failed/rejected records
      if (result.invalidCount > 0) {

        result.invalidReasons.forEach((reason, index) => {
          console.log(`   ${index + 1}. ${reason}`);
        });

      }

      if (result.validRecords.length > 0) {
        await this.storeEmployeeData(result.validRecords, batchSize);
      }
    } catch (error) {
      const fileInfo = fileName ? ` from file: ${fileName}` : '';
      console.error(`❌ Error processing employee data${fileInfo}:`, error);
      throw error;
    }
  }

  // Get active employees
  async getActiveEmployees(): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {employeeStatus: 'Active'}
    });
  }

  // Get inactive employees
  async getInactiveEmployees(): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {employeeStatus: 'Inactive'}
    });
  }

  // Get employees by location
  async getEmployeesByLocation(locationId: number): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {locationId}
    });
  }

  // Generate employee analytics data based on reporting period and date range
  async getEmployeeAnalytics(
    type: number, // 1-monthly, 2-Bi-Monthly, 3-Quarterly, 4-Annually, 5-Bi-Annually
    fromDate: string, // ddMyyyy format
    toDate: string // ddMyyyy format
  ): Promise<any[]> {
    try {
      // Parse dates
      const startDate = DateTime.fromFormat(fromDate, 'ddMMyyyy');
      const endDate = DateTime.fromFormat(toDate, 'ddMMyyyy');

      if (!startDate.isValid || !endDate.isValid) {
        throw new Error('Invalid date format. Use ddMMyyyy format.');
      }

      // Generate periods based on type
      const periods = this.generatePeriods(type, startDate, endDate);
      const allDataPoints: any[] = [];

      // Process each period (but only collect datapoints, not period metadata)
      for (const period of periods) {
        const periodData = await this.calculatePeriodAnalytics(period);
        if (Array.isArray(periodData.dataPoints)) {
          allDataPoints.push(...periodData.dataPoints);
        }
      }

      return allDataPoints;
    } catch (error) {
      console.error('Error generating employee analytics:', error);
      throw error;
    }
  }

  // Generate periods based on reporting type
  private generatePeriods(type: number, startDate: DateTime, endDate: DateTime): any[] {
    const periods: any[] = [];
    let currentDate = startDate.startOf('month');
    const now = DateTime.now().endOf('month'); // Last day of current month

    while (currentDate <= endDate) {
      let periodEnd: DateTime;
      let periodName: string;

      switch (type) {
        case 1: // Monthly
          periodEnd = currentDate.endOf('month');
          periodName = currentDate.toFormat('MMM yyyy');
          currentDate = currentDate.plus({months: 1});
          break;
        case 2: // Bi-Monthly
          periodEnd = currentDate.plus({months: 1}).endOf('month');
          periodName = `${currentDate.toFormat('MMM')}-${periodEnd.toFormat('MMM yyyy')}`;
          currentDate = currentDate.plus({months: 2});
          break;
        case 3: // Quarterly
          periodEnd = currentDate.plus({months: 2}).endOf('month');
          periodName = `Q${Math.ceil(currentDate.month / 3)} ${currentDate.year}`;
          currentDate = currentDate.plus({months: 3});
          break;
        case 4: // Annually
          periodEnd = currentDate.endOf('year');
          periodName = currentDate.toFormat('yyyy');
          currentDate = currentDate.plus({years: 1});
          break;
        case 5: // Bi-Annually
          periodEnd = currentDate.plus({months: 5}).endOf('month');
          periodName = `H${Math.ceil(currentDate.month / 6)} ${currentDate.year}`;
          currentDate = currentDate.plus({months: 6});
          break;
        default:
          throw new Error('Invalid type. Use 1-5 for different reporting periods.');
      }

      if (periodEnd > endDate) {
        periodEnd = endDate;
      }

      // Do not include periods that end after the current month
      if (periodEnd > now) {
        break;
      }

      periods.push({
        periodName,
        startDate: currentDate.minus({months: type === 1 ? 1 : type === 2 ? 2 : type === 3 ? 3 : type === 4 ? 12 : 6}),
        endDate: periodEnd
      });

      if (currentDate > endDate) break;
    }

    return periods;
  }

  // Calculate analytics for a specific period
  private async calculatePeriodAnalytics(period: any): Promise<any> {
    const dataPoints: any[] = [];

    const formCategory = 2;
    const unitOfMeasure = 'nos';
    const entity = 'India';
    const status = 'Approved';

    // Helper to get rp array from period.periodName
    const getRPArray = (periodName: string): string[] => {
      // Handles 'Mar 2025', 'Mar-2025', 'Mar-2025 to Apr-2025', etc.
      if (!periodName) return [];
      if (periodName.includes(' to ')) {
        // e.g., 'Mar-2025 to Apr-2025'
        return periodName.split(' to ').map(s => {
          // Accept both 'Mar-2025' and 'Mar 2025'
          const clean = s.replace(/\s+/, '-');
          return DateTime.fromFormat(clean, 'MMM-yyyy').toFormat('MM-yyyy');
        });
      } else if (periodName.includes('-')) {
        // e.g., 'Mar-2025'
        return [DateTime.fromFormat(periodName, 'MMM-yyyy').toFormat('MM-yyyy')];
      } else if (periodName.match(/^[A-Za-z]{3} \d{4}$/)) {
        // e.g., 'Mar 2025'
        return [DateTime.fromFormat(periodName, 'MMM yyyy').toFormat('MM-yyyy')];
      } else {
        return [periodName];
      }
    };

    const rpArr = getRPArray(period.periodName);
    const periodFrom = rpArr.length > 0 ? rpArr[0] : '';
    const periodTo = rpArr.length > 1 ? rpArr[rpArr.length - 1] : periodFrom;

    // Get categories for each data type
    const totalCategories = this.getTotalCategories();
    const hireCategories = this.getHireCategories();
    const turnoverCategories = this.getTurnoverCategories();

    // Calculate Total employees
    for (const category of totalCategories) {
      const totalCount = await this.calculateTotalEmployees(period, category);
      dataPoints.push({
        uniqueId: category.uniqueId,
        type: 1,
        formId: "SAP8", sapId: 8,
        formCategory, title: category.Title,
        dataPoint: category.Title,
        unitOfMeasure,
        value: totalCount,
        entity,
        rp: rpArr,
        periodFrom,
        periodTo,
        status,
      });
    }

    // Calculate New Employee Hire
    for (const category of hireCategories) {
      const newHireCount = await this.calculateNewHires(period, category);
      dataPoints.push({
        uniqueId: category.uniqueId,
        type: 2,
        formId: "SAP9", sapId: 9,
        formCategory, title: category.Title,
        dataPoint: category.Title,
        unitOfMeasure,
        value: newHireCount,
        entity,
        rp: rpArr,
        periodFrom,
        periodTo,
        status,
      });
    }

    // Calculate Employee Turnover
    for (const category of turnoverCategories) {
      const turnoverCount = await this.calculateTurnover(period, category);
      dataPoints.push({
        uniqueId: category.uniqueId,
        type: 3,
        formId: "SAP10", sapId: 10,
        formCategory, title: category.Title,
        dataPoint: category.Title,
        unitOfMeasure,
        value: turnoverCount,
        entity,
        rp: rpArr,
        periodFrom,
        periodTo,
        status,
      });
    }

    return {
      period: period.periodName,
      startDate: period.startDate.toISODate(),
      endDate: period.endDate.toISODate(),
      dataPoints: dataPoints,
      totalDataPoints: dataPoints.length
    };
  }

  // Categories for Total Employees
  private getTotalCategories(): any[] {
    return [
      // Employment Type by Gender (4 categories) - ALL employees regardless of role type
      {Title: 'Permanent Male Employees', uniqueId: 'DPTVS0090', filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee', EmployeeGender: 'Male'}},
      {Title: 'Permanent Female Employees', uniqueId: 'DPTVS0091', filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee', EmployeeGender: 'Female'}},
      {Title: 'Other than Permanent Male Employees', uniqueId: 'DPTVS0092', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Male'}},
      {Title: 'Other than Permanent Female Employees', uniqueId: 'DPTVS0093', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Female'}},

      // Senior Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'Senior Management Male <30 years', uniqueId: 'DPTVS0094', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Senior Management Female <30 years', uniqueId: 'DPTVS0095', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Senior Management Male 30-50 Years', uniqueId: 'DPTVS0096', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Senior Management Female 30-50 Years', uniqueId: 'DPTVS0097', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Senior Management Male >50 years', uniqueId: 'DPTVS0098', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'Senior Management Female >50 years', uniqueId: 'DPTVS0099', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Middle Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'Middle Management Male <30 years', uniqueId: 'DPTVS0100', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Middle Management Female <30 years', uniqueId: 'DPTVS0101', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Middle Management Male 30-50 Years', uniqueId: 'DPTVS0102', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Middle Management Female 30-50 Years', uniqueId: 'DPTVS0103', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Middle Management Male >50 years', uniqueId: 'DPTVS0104', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'Middle Management Female >50 years', uniqueId: 'DPTVS0105', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Non-Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'Non-Management Male <30 years', uniqueId: 'DPTVS0112', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Non-Management Female <30 years', uniqueId: 'DPTVS0113', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Non-Management Male 30-50 Years', uniqueId: 'DPTVS0114', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Non-Management Female 30-50 Years', uniqueId: 'DPTVS0115', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Non-Management Male >50 years', uniqueId: 'DPTVS0116', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'Non-Management Female >50 years', uniqueId: 'DPTVS0117', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Permanent Workers
      {Title: 'Permanent Male Workers', uniqueId: 'DPTVS0118', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Permanent'}},
      {Title: 'Permanent Female Workers', uniqueId: 'DPTVS0119', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Permanent'}},
      {Title: 'Other Permanent Male Workers', uniqueId: 'DPTVS0120', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Permanent'}},
      {Title: 'Other Permanent Female Workers', uniqueId: 'DPTVS0121', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Permanent'}},
    ];
  }

  // Categories for New Employee Hire
  private getHireCategories(): any[] {
    return [
      // Employment Type by Gender (4 categories) - ALL employees regardless of role type
      {Title: 'New Employee Hires - Permanent Male Employees', uniqueId: 'DPTVS700', filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee', EmployeeGender: 'Male'}},
      {Title: 'New Employee Hires - Permanent Female Employees', uniqueId: 'DPTVS716', filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee', EmployeeGender: 'Female'}},
      {Title: 'New Employee Hires - Other than Permanent Male Employees', uniqueId: 'DPTVS701', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Male'}},
      {Title: 'New Employee Hires - Other than Permanent Female Employees', uniqueId: 'DPTVS717', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Female'}},

      // Senior Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'New Employee Hires - Senior Management Male <30 years', uniqueId: 'DPTVS702', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'New Employee Hires - Senior Management Female <30 years', uniqueId: 'DPTVS718', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'New Employee Hires - Senior Management Male 30-50 Years', uniqueId: 'DPTVS703', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'New Employee Hires - Senior Management Female 30-50 Years', uniqueId: 'DPTVS719', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'New Employee Hires - Senior Management Male >50 years', uniqueId: 'DPTVS704', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'New Employee Hires - Senior Management Female >50 years', uniqueId: 'DPTVS720', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Middle Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'New Employee Hires - Middle Management Male <30 years', uniqueId: 'DPTVS705', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'New Employee Hires - Middle Management Female <30 years', uniqueId: 'DPTVS721', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'New Employee Hires - Middle Management Male 30-50 Years', uniqueId: 'DPTVS706', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'New Employee Hires - Middle Management Female 30-50 Years', uniqueId: 'DPTVS722', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'New Employee Hires - Middle Management Male >50 years', uniqueId: 'DPTVS707', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'New Employee Hires - Middle Management Female >50 years', uniqueId: 'DPTVS723', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Non-Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'New Employee Hires - Non-Management Male <30 years', uniqueId: 'DPTVS708', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'New Employee Hires - Non-Management Female <30 years', uniqueId: 'DPTVS724', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'New Employee Hires - Non-Management Male 30-50 Years', uniqueId: 'DPTVS709', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'New Employee Hires - Non-Management Female 30-50 Years', uniqueId: 'DPTVS725', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'New Employee Hires - Non-Management Male >50 years', uniqueId: 'DPTVS710', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'New Employee Hires - Non-Management Female >50 years', uniqueId: 'DPTVS726', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Permanent Workers
      {Title: 'New Employee Hires - Permanent Male Workers', uniqueId: 'DPTVS714', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Permanent'}},
      {Title: 'New Employee Hires - Permanent Female Workers', uniqueId: 'DPTVS730', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Permanent'}},
      {Title: 'New Employee Hires - Other Permanent Male Workers', uniqueId: 'DPTVS715', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Permanent'}},
      {Title: 'New Employee Hires - Other Permanent Female Workers', uniqueId: 'DPTVS731', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Permanent'}},
    ];
  }

  // Categories for Employee Turnover
  private getTurnoverCategories(): any[] {
    return [
      // Employment Type by Gender (4 categories) - ALL employees regardless of role type
      {Title: 'Employee Turnover - Permanent Male Employees', dpId: 'DPTVS732', filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee', EmployeeGender: 'Male'}},
      {Title: 'Employee Turnover - Permanent Female Employees', dpId: 'DPTVS748', filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee', EmployeeGender: 'Female'}},
      {Title: 'Employee Turnover - Other than Permanent Male Employees', dpId: 'DPTVS733', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Male'}},
      {Title: 'Employee Turnover - Other than Permanent Female Employees', dpId: 'DPTVS749', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Female'}},

      // Senior Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'Employee Turnover - Senior Management Male <30 years', dpId: 'DPTVS734', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Employee Turnover - Senior Management Female <30 years', dpId: 'DPTVS750', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Employee Turnover - Senior Management Male 30-50 Years', dpId: 'DPTVS735', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Employee Turnover - Senior Management Female 30-50 Years', dpId: 'DPTVS751', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Employee Turnover - Senior Management Male >50 years', dpId: 'DPTVS736', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'Employee Turnover - Senior Management Female >50 years', dpId: 'DPTVS752', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Middle Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'Employee Turnover - Middle Management Male <30 years', dpId: 'DPTVS737', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Employee Turnover - Middle Management Female <30 years', dpId: 'DPTVS753', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Employee Turnover - Middle Management Male 30-50 Years', dpId: 'DPTVS738', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Employee Turnover - Middle Management Female 30-50 Years', dpId: 'DPTVS754', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Employee Turnover - Middle Management Male >50 years', dpId: 'DPTVS739', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'Employee Turnover - Middle Management Female >50 years', dpId: 'DPTVS755', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Non-Management by Gender and Age (6 categories) - Only Permanent Employees
      {Title: 'Employee Turnover - Non-Management Male <30 years', dpId: 'DPTVS740', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Employee Turnover - Non-Management Female <30 years', dpId: 'DPTVS756', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {Title: 'Employee Turnover - Non-Management Male 30-50 Years', dpId: 'DPTVS741', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Employee Turnover - Non-Management Female 30-50 Years', dpId: 'DPTVS757', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {Title: 'Employee Turnover - Non-Management Male >50 years', dpId: 'DPTVS742', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {Title: 'Employee Turnover - Non-Management Female >50 years', dpId: 'DPTVS758', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Permanent Workers
      {Title: 'Employee Turnover - Permanent Male Workers', dpId: 'DPTVS746', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Permanent'}},
      {Title: 'Employee Turnover - Permanent Female Workers', dpId: 'DPTVS762', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Permanent'}},
      {Title: 'Employee Turnover - Other Permanent Male Workers', dpId: 'DPTVS747', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Permanent'}},
      {Title: 'Employee Turnover - Other Permanent Female Workers', dpId: 'DPTVS763', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Permanent'}},
    ];
  }

  // Calculate total employees for a category in a period
  public async calculateTotalEmployees(period: any, category: any): Promise<number> {
    // Use raw SQL query for proper date and filter handling
    let sql = `
      SELECT COUNT(*) as count
      FROM EmployeeData
      WHERE 1=1
    `;
    const params: any[] = [];

    // Add category filters first
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering - EmployeeAge is stored as string but treat as number
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) < 30`;
        } else if (ageGroup === '30-50') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) >= 30 AND CAST(EmployeeAge AS SIGNED) <= 50`;
        } else if (ageGroup === '>50') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) > 50`;
        }
      } else {
        // Apply all other filters including EmployeeCategory: 'Permanent'
        sql += ` AND ${key} = ?`;
        params.push(category.filters[key]);
      }
    });

    // Add date filtering - convert DD-MM-YYYY to comparable format
    const periodEndDate = period.endDate.toFormat('yyyy-MM-dd');
    sql += ` AND STR_TO_DATE(EmployeeDOJ, '%d-%m-%Y') <= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodEndDate);

    sql += ` AND (EmployeeDOE IS NULL OR STR_TO_DATE(EmployeeDOE, '%d-%m-%Y') > STR_TO_DATE(?, '%Y-%m-%d'))`;
    params.push(periodEndDate);

    const result = await this.employeeDataRepository.execute(sql, params);
    return result[0]?.count || 0;
  }

  // Calculate new hires for a category in a period (only those who joined in this specific period)
  private async calculateNewHires(period: any, category: any): Promise<number> {
    // Use raw SQL query for proper date and filter handling
    let sql = `
      SELECT COUNT(*) as count
      FROM EmployeeData
      WHERE EmployeeDOJ IS NOT NULL
    `;
    const params: any[] = [];

    // Add category filters first
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering - EmployeeAge is stored as string but treat as number
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) < 30`;
        } else if (ageGroup === '30-50') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) >= 30 AND CAST(EmployeeAge AS SIGNED) <= 50`;
        } else if (ageGroup === '>50') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) > 50`;
        }
      } else {
        // Apply all other filters including EmployeeCategory: 'Permanent'
        sql += ` AND ${key} = ?`;
        params.push(category.filters[key]);
      }
    });

    // Add date filtering for new hires - joined within this period
    const periodStartDate = period.startDate.toFormat('yyyy-MM-dd');
    const periodEndDate = period.endDate.toFormat('yyyy-MM-dd');

    sql += ` AND STR_TO_DATE(EmployeeDOJ, '%d-%m-%Y') >= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodStartDate);

    sql += ` AND STR_TO_DATE(EmployeeDOJ, '%d-%m-%Y') <= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodEndDate);

    const result = await this.employeeDataRepository.execute(sql, params);
    return result[0]?.count || 0;
  }

  // Calculate employee turnover for a category in a period (only those who left in this specific period)
  public async calculateTurnover(period: any, category: any): Promise<number> {
    // Use raw SQL query for proper date and filter handling
    let sql = `
      SELECT COUNT(*) as count
      FROM EmployeeData
      WHERE EmployeeDOE IS NOT NULL
      AND employeeStatus = 'Inactive'
    `;
    const params: any[] = [];

    // Add category filters first
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering - EmployeeAge is stored as string but treat as number
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) < 30`;
        } else if (ageGroup === '30-50') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) >= 30 AND CAST(EmployeeAge AS SIGNED) <= 50`;
        } else if (ageGroup === '>50') {
          sql += ` AND CAST(EmployeeAge AS SIGNED) > 50`;
        }
      } else {
        // Apply all other filters including EmployeeCategory: 'Permanent'
        sql += ` AND ${key} = ?`;
        params.push(category.filters[key]);
      }
    });

    // Add date filtering for turnover - left within this period
    const periodStartDate = period.startDate.toFormat('yyyy-MM-dd');
    const periodEndDate = period.endDate.toFormat('yyyy-MM-dd');

    sql += ` AND STR_TO_DATE(EmployeeDOE, '%d-%m-%Y') >= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodStartDate);

    sql += ` AND STR_TO_DATE(EmployeeDOE, '%d-%m-%Y') <= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodEndDate);

    const result = await this.employeeDataRepository.execute(sql, params);
    return result[0]?.count || 0;
  }

  // Reprocess all employee grades with updated mapping logic
  async reprocessAllGrades(): Promise<{message: string, updated: number}> {
    try {
      // Get all employees
      const allEmployees = await this.employeeDataRepository.find();
      let updatedCount = 0;



      // Process in batches to avoid memory issues
      const batchSize = 100;
      for (let i = 0; i < allEmployees.length; i += batchSize) {
        const batch = allEmployees.slice(i, i + batchSize);

        for (const employee of batch) {
          // Recalculate the grade using the updated mapping
          const newGrade = this.mapGradeToEmployeeGrade(
            employee.raw_grade || '',
            employee.EmployeeCategory,
            employee.EmployeeRoleType
          );

          // Debug logging for first few records


          // Only update if the grade has changed
          if (employee.EmployeeGrade !== newGrade) {
            await this.employeeDataRepository.updateById(employee.id, {
              EmployeeGrade: newGrade
            });
            updatedCount++;

            // Debug logging for updates

          }
        }

      }


      return {
        message: `Successfully reprocessed grades for ${allEmployees.length} employees. Updated ${updatedCount} records.`,
        updated: updatedCount
      };
    } catch (error) {
      console.error('Error reprocessing grades:', error);
      throw error;
    }
  }

  // Test grade mapping for debugging
  async testGradeMapping(grade: string): Promise<{inputGrade: string, mappedGrade: string}> {
    const mappedGrade = this.mapGradeToEmployeeGrade(grade, 'Permanent', 'Employee');
    return {
      inputGrade: grade,
      mappedGrade: mappedGrade
    };
  }

  /**
   * Calculate Retention Rate for the given period and type
   * Retention Rate = (Permanent Employees at end of current period) / (Permanent Employees at end of previous period) * 100
   * Returns: { rate: number, employeesAtEnd: number, employeesAtPrevEnd: number }
   */
  async calculateRetentionRate({type, fromDate, toDate}: {type: number, fromDate: string, toDate: string}): Promise<any> {
    // Get periods for the requested range
    const periods = this.generatePeriods(type, DateTime.fromFormat(fromDate, 'ddMMyyyy'), DateTime.fromFormat(toDate, 'ddMMyyyy'));
    if (periods.length === 0) return {rate: 0, employeesAtEnd: 0, employeesAtPrevEnd: 0};
    const currentPeriod = periods[0];
    // Find previous reporting period (immediately before currentPeriod.startDate, always monthly)
    const prevStart = currentPeriod.startDate.minus({months: 1}).startOf('month');
    const prevEnd = currentPeriod.startDate.minus({months: 1}).endOf('month');
    // Use Permanent Employees only
    const baseCategory = {filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee'}};
    // Employees at end of current period
    const employeesAtEnd = await this.calculateTotalEmployees(currentPeriod, baseCategory);
    // Employees at end of previous period (monthly, regardless of frequency)
    const employeesAtPrevEnd = await this.calculateTotalEmployees(prevEnd, baseCategory);
    // Retention Rate formula
    const rate = employeesAtPrevEnd > 0 ? ((employeesAtEnd / employeesAtPrevEnd) * 100) : 0;
    return {
      rate: Math.round(rate * 100) / 100,
      employeesAtEnd,
      employeesAtPrevEnd
    };
  }

  /**
   * Calculate Attrition Rate for the given period and type
   * Attrition Rate = Leavers during period / Average employees during period
   * Returns: { rate: number, leavers: number, avgEmployees: number }
   */
  async calculateAttritionRate({type, fromDate, toDate}: {type: number, fromDate: string, toDate: string}): Promise<any> {
    // Get periods (should be only one for the requested range)
    const periods = this.generatePeriods(type, DateTime.fromFormat(fromDate, 'ddMMyyyy'), DateTime.fromFormat(toDate, 'ddMMyyyy'));
    if (periods.length === 0) return {rate: 0, leavers: 0, avgEmployees: 0};
    const period = periods[0];
    // Use Permanent Employees only for Attrition Rate
    const baseCategory = {filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee'}};
    // Employees at start
    const employeesAtStart = await this.calculateTotalEmployees(period, baseCategory);
    // Employees at end
    const employeesAtEnd = await this.calculateTotalEmployees(period, baseCategory);
    // Leavers during period
    const leavers = await this.calculateTurnover(period, baseCategory);
    // Average employees
    const avgEmployees = (employeesAtStart + employeesAtEnd) / 2;
    // Attrition Rate formula
    const rate = avgEmployees > 0 ? ((leavers / avgEmployees) * 100) : 0;
    return {
      rate: Math.round(rate * 100) / 100,
      leavers,
      avgEmployees: Math.round(avgEmployees * 100) / 100
    };
  }
}
