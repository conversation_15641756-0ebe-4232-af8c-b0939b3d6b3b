# Bulk Update UserProfileId Endpoints

## Overview
These endpoints allow you to update the `userProfileId` field across all repositories and models that have relationships or properties referencing user profiles. This is useful for scenarios like user merging, data migration, or correcting user references.

## Available Endpoints

### 1. Repository-Based Update (Complete ORM Coverage) ⭐ **RECOMMENDED FOR SAFETY**
- **URL**: `POST /bulk-update-user-profile-id`
- **Authentication**: Required (JWT)
- **Content-Type**: `application/json`
- **Coverage**: **ALL 58+ repositories** with userProfileId properties
- **Best for**: When you need ORM validation, error handling, and data integrity

### 2. Comprehensive SQL-Based Update (Direct SQL) ⭐ **RECOMMENDED FOR PERFORMANCE**
- **URL**: `POST /bulk-update-user-profile-id-comprehensive`
- **Authentication**: Required (JWT)
- **Content-Type**: `application/json`
- **Coverage**: All 58 tables with userProfileId columns (same as #1)
- **Best for**: Maximum performance for large datasets, direct SQL execution

## Request Body
```json
{
  "oldUserProfileId": 123,
  "newUserProfileId": 456
}
```

### Parameters
- `oldUserProfileId` (number, required): The current userProfileId that needs to be replaced
- `newUserProfileId` (number, required): The new userProfileId to replace with

## Response Format
```json
{
  "status": true,
  "message": "Successfully updated userProfileId from 123 to 456 across 45 records",
  "updatedCounts": {
    "LocationOne": 5,
    "SubmitDcf": 12,
    "SubmitRf": 8,
    "SubmitCf": 3,
    "AssignQlEntity": 7,
    "AssignQlEntityUser": 4,
    "Response": 2,
    "AssignDcfEntityUser": 3,
    "AssignDcfUser": 1,
    "UserRoleAuthorization": 0,
    "VendorCode": 0
  },
  "errors": []
}
```

### Response Fields
- `status` (boolean): Indicates if the operation was successful
- `message` (string): Human-readable description of the operation result
- `updatedCounts` (object): Count of records updated in each repository
- `errors` (array): List of any errors encountered during the operation

## Comprehensive Endpoint Response Format
```json
{
  "status": true,
  "message": "Successfully updated userProfileId from 123 to 456 across 127 records in 58 tables",
  "updatedTables": {
    "assign_dcf_client": 2,
    "assign_dcf_entity_user": 15,
    "assign_dcf_entity": 8,
    "assign_dcf_suppliers": 3,
    "location_one": 5,
    "submit_dcf": 12,
    "submit_rf": 8,
    "vendor_code": 1,
    // ... all other tables
  },
  "totalUpdated": 127,
  "errors": []
}
```

## Affected Tables/Models
The comprehensive endpoint updates **58 tables** that have `userProfileId` columns:

### Assignment Tables
- `assign_dcf_client`, `assign_dcf_entity_user`, `assign_dcf_entity`, `assign_dcf_suppliers`
- `assign_dcf_user_new`, `assign_dcf_user`, `assign_df_entity_user`, `assign_df_entity`
- `assign_df_user`, `assign_ql_entity_user`, `assign_ql_entity`, `assign_rf_entity`
- `assign_rf_users`, `assign_srf_entity_user`, `assign_srf_entity`, `assign_srf_user`

### Submission Tables
- `submit_cf`, `submit_dcf`, `submit_dcf_new`, `submit_rf`, `submit_rf_new`
- `auditor_assignment_submission`, `supplier_assignment_submission`
- `supplier_section_submission`, `dealer_checklist_submission`
- `dealer_auditor_checklist_submission`

### Assessment & Approval Tables
- `dealer_assessment_assignment`, `supplier_assessment_assignment`
- `qualitative_approval`, `qualitative_submission`, `qn_indicator_approval`
- `quantitative_dp_report`, `quantitative_submission`

### Configuration & Management Tables
- `client_ef_category_assignment`, `client_ef_category_mapping`, `client_initiative`
- `computed_indicator`, `delete_user_log`, `employee_data`, `frequency`
- `helper`, `indicator_approver_assignment`, `indicator_section`
- `location_one`, `new_client_certification`, `new_goals`, `news_circulation`
- `policy_procedure`, `ql_listing_filter`, `response`, `sap_collection`
- `sap_response`, `structured_response`, `ticketing`, `user_role_authorization`
- `value_chain_submission`, `vendor_code`

### Report Tables
- `dp_report`, `dp_report_new`, `dealer_response_form`

## Usage Examples

### Example 1: Repository-Based Update (Smaller datasets)
```bash
curl -X POST http://localhost:3000/bulk-update-user-profile-id \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "oldUserProfileId": 123,
    "newUserProfileId": 456
  }'
```

### Example 2: Comprehensive SQL-Based Update (Large datasets)
```bash
curl -X POST http://localhost:3000/bulk-update-user-profile-id-comprehensive \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "oldUserProfileId": 123,
    "newUserProfileId": 456
  }'
```

### Example 2: Error Response (User Not Found)
```json
{
  "status": false,
  "message": "Old UserProfile with ID 999 not found",
  "updatedCounts": {},
  "errors": []
}
```

### Example 3: Partial Success with Errors
```json
{
  "status": false,
  "message": "Completed with 1 errors. Updated 42 records total.",
  "updatedCounts": {
    "LocationOne": 5,
    "SubmitDcf": 12,
    "SubmitRf": 8,
    "SubmitCf": 3,
    "AssignQlEntity": 7,
    "AssignQlEntityUser": 4,
    "Response": 2,
    "AssignDcfEntityUser": 3,
    "AssignDcfUser": 0,
    "UserRoleAuthorization": 0,
    "VendorCode": 0
  },
  "errors": [
    "Failed to update AssignDcfUser: Database connection timeout"
  ]
}
```

## Important Notes

1. **Validation**: Both endpoints validate that old and new UserProfile IDs exist before performing any updates.

2. **Performance Comparison**:
   - **Repository-based**: Better for smaller datasets (< 1000 records), uses ORM validation
   - **Comprehensive SQL**: Better for large datasets (> 1000 records), direct SQL execution

3. **Atomic Operations**: Each table/repository update is performed separately. If one fails, others may still succeed.

4. **Error Handling**: Both endpoints continue processing even if individual updates fail, providing complete reports.

5. **Coverage**: The comprehensive endpoint covers **58 tables** vs 11 repositories in the basic endpoint.

6. **Performance**: For large datasets, use the comprehensive endpoint during off-peak hours.

7. **Backup**: Always backup your database before performing bulk updates.

8. **Monitoring**: The comprehensive endpoint provides detailed table-by-table update counts.

## Security Considerations

- This endpoint requires JWT authentication
- Only authorized users should have access to this endpoint
- Consider adding additional role-based authorization if needed
- Log all bulk update operations for audit purposes

## Troubleshooting

### Common Issues
1. **User Not Found**: Ensure both old and new UserProfile IDs exist in the database
2. **Permission Denied**: Verify JWT token is valid and user has appropriate permissions
3. **Database Timeout**: For large updates, consider increasing database timeout settings
4. **Foreign Key Constraints**: Ensure the new UserProfile ID doesn't violate any database constraints

### Monitoring
- Check the `updatedCounts` object to verify expected number of records were updated
- Review the `errors` array for any failed operations
- Monitor database performance during bulk operations
